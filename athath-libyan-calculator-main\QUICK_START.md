# دليل البدء السريع - تطبيق سطح المكتب

## 🚀 تشغيل التطبيق بسرعة

### الطريقة الأولى: تشغيل مباشر في وضع التطوير
```bash
# 1. تثبيت التبعيات
npm install

# 2. تشغيل التطبيق
npm run electron:dev
```

### الطريقة الثانية: بناء التطبيق كاملاً
```bash
# بناء تطبيق سطح المكتب (ملف تثبيت)
npm run build:desktop
```

## 📋 الأوامر المهمة

| الأمر | الوصف |
|-------|--------|
| `npm run electron:dev` | تشغيل التطبيق في وضع التطوير |
| `npm run build:desktop` | بناء ملف التثبيت |
| `npm run clean` | مسح ملفات البناء |
| `npm run electron` | تشغيل Electron مع الملفات المبنية |

## 🔧 استكشاف الأخطاء

### مشكلة: التطبيق لا يعمل
```bash
npm run clean
npm install
npm run electron:dev
```

### مشكلة: فشل في البناء
```bash
# تأكد من وجود جميع الملفات
ls public/electron.js
ls public/preload.js

# أعد البناء
npm run build
npm run electron:pack
```

## 📁 ملفات التثبيت

بعد تشغيل `npm run build:desktop`، ستجد ملفات التثبيت في:
```
dist-electron/
├── نظام إدارة مصنع الأثاث-1.0.0-Setup.exe  (Windows)
├── نظام إدارة مصنع الأثاث-1.0.0.dmg         (macOS)
└── نظام إدارة مصنع الأثاث-1.0.0.AppImage     (Linux)
```

## ⚡ نصائح سريعة

1. **للتطوير**: استخدم `npm run electron:dev`
2. **للتوزيع**: استخدم `npm run build:desktop`
3. **لحل المشاكل**: استخدم `npm run clean` ثم أعد التثبيت

## 🎯 الميزات الجديدة في تطبيق سطح المكتب

- ✅ قائمة عربية كاملة
- ✅ اختصارات لوحة المفاتيح
- ✅ حفظ وفتح الملفات
- ✅ طباعة التقارير
- ✅ إشعارات النظام
- ✅ نافذة مخصصة
- ✅ أمان محسن
