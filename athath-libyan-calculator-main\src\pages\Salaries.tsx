import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { ArrowLeft, User, Plus, Edit, Trash2, Calculator, Download, Calendar, DollarSign, Minus, TrendingUp, CheckCircle } from "lucide-react";
import { Link } from "react-router-dom";
import Navbar from "@/components/Navbar";
import { formatLibyanDinar } from "@/utils/calculations";
import { getEmployees, saveEmployees, Employee, addCashTransaction, CashTransaction, paySalary } from "@/utils/dataManager";
import { useToast } from "@/hooks/use-toast";

const Salaries = () => {
  const { toast } = useToast();
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [isAdding, setIsAdding] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [selectedMonth, setSelectedMonth] = useState<string>(new Date().getMonth().toString());
  const [selectedYear, setSelectedYear] = useState<string>(new Date().getFullYear().toString());
  const [actionDialog, setActionDialog] = useState<{
    isOpen: boolean;
    type: 'deduction' | 'bonus' | 'payment' | null;
    employee: Employee | null;
  }>({
    isOpen: false,
    type: null,
    employee: null
  });
  
  const [actionForm, setActionForm] = useState({
    amount: "",
    description: "",
    notes: ""
  });

  const [formData, setFormData] = useState({
    name: "",
    position: "",
    baseSalary: "",
    bonuses: "",
    deductions: "",
    phone: ""
  });

  useEffect(() => {
    loadEmployees();
  }, []);

  const loadEmployees = () => {
    const savedEmployees = getEmployees();
    // إضافة بيانات افتراضية إذا لم تكن موجودة
    if (savedEmployees.length === 0) {
      const defaultEmployees: Employee[] = [
        {
          id: "1",
          name: "محمد أحمد",
          position: "نجار رئيسي",
          baseSalary: 1500,
          bonuses: 200,
          deductions: 50,
          totalSalary: 1650,
          phone: "************",
          hireDate: "2023-01-15"
        },
        {
          id: "2",
          name: "علي حسن",
          position: "مساعد نجار",
          baseSalary: 1000,
          bonuses: 100,
          deductions: 30,
          totalSalary: 1070,
          phone: "************",
          hireDate: "2023-03-20"
        }
      ];
      setEmployees(defaultEmployees);
      saveEmployees(defaultEmployees);
    } else {
      setEmployees(savedEmployees);
    }
  };

  const calculateTotalSalary = (base: number, bonuses: number, deductions: number) => {
    return base + bonuses - deductions;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.position || !formData.baseSalary) return;

    const base = parseFloat(formData.baseSalary);
    const bonuses = parseFloat(formData.bonuses) || 0;
    const deductions = parseFloat(formData.deductions) || 0;
    const total = calculateTotalSalary(base, bonuses, deductions);

    const newEmployee: Employee = {
      id: editingId || Date.now().toString(),
      name: formData.name,
      position: formData.position,
      baseSalary: base,
      bonuses: bonuses,
      deductions: deductions,
      totalSalary: total,
      phone: formData.phone || undefined,
      hireDate: editingId 
        ? employees.find(emp => emp.id === editingId)?.hireDate || new Date().toISOString().split('T')[0]
        : new Date().toISOString().split('T')[0]
    };

    const updatedEmployees = editingId 
      ? employees.map(emp => emp.id === editingId ? newEmployee : emp)
      : [...employees, newEmployee];

    setEmployees(updatedEmployees);
    saveEmployees(updatedEmployees);
    handleCancel();
    
    toast({
      title: editingId ? "تم تحديث الموظف" : "تم إضافة الموظف",
      description: "تم حفظ البيانات بنجاح"
    });
  };

  const handleEdit = (employee: Employee) => {
    setFormData({
      name: employee.name,
      position: employee.position,
      baseSalary: employee.baseSalary.toString(),
      bonuses: employee.bonuses.toString(),
      deductions: employee.deductions.toString(),
      phone: employee.phone || ""
    });
    setEditingId(employee.id);
    setIsAdding(true);
  };

  const handleDelete = (id: string) => {
    const updatedEmployees = employees.filter(emp => emp.id !== id);
    setEmployees(updatedEmployees);
    saveEmployees(updatedEmployees);
    toast({
      title: "تم حذف الموظف",
      description: "تم حذف الموظف بنجاح"
    });
  };

  const handleCancel = () => {
    setFormData({ name: "", position: "", baseSalary: "", bonuses: "", deductions: "", phone: "" });
    setIsAdding(false);
    setEditingId(null);
  };

  const openActionDialog = (type: 'deduction' | 'bonus' | 'payment', employee: Employee) => {
    setActionDialog({ isOpen: true, type, employee });
    setActionForm({ amount: "", description: "", notes: "" });
  };

  const closeActionDialog = () => {
    setActionDialog({ isOpen: false, type: null, employee: null });
    setActionForm({ amount: "", description: "", notes: "" });
  };

  const handlePayrollAction = () => {
    if (!actionDialog.employee || !actionForm.amount) return;

    const amount = parseFloat(actionForm.amount);
    const employee = actionDialog.employee;
    
    let updatedEmployees = [...employees];
    const employeeIndex = updatedEmployees.findIndex(emp => emp.id === employee.id);
    
    if (employeeIndex === -1) return;

    let transactionType: 'دخل' | 'مصروف' = 'مصروف';
    let description = "";
    
    switch (actionDialog.type) {
      case 'deduction':
        updatedEmployees[employeeIndex].deductions += amount;
        description = `خصم من راتب ${employee.name} - ${actionForm.description}`;
        transactionType = 'دخل'; // الخصم يعتبر دخل للشركة
        break;
      
      case 'bonus':
        updatedEmployees[employeeIndex].bonuses += amount;
        description = `علاوة لـ ${employee.name} - ${actionForm.description}`;
        transactionType = 'مصروف';
        break;
      
      case 'payment':
        description = `دفع راتب ${employee.name}`;
        transactionType = 'مصروف';
        // دفع الراتب لا يغير في بيانات الموظف، فقط يسجل في الخزينة
        paySalary(employee.id, amount, actionForm.notes);
        break;
    }

    // تحديث إجمالي الراتب للخصم والعلاوة
    if (actionDialog.type !== 'payment') {
      updatedEmployees[employeeIndex].totalSalary = calculateTotalSalary(
        updatedEmployees[employeeIndex].baseSalary,
        updatedEmployees[employeeIndex].bonuses,
        updatedEmployees[employeeIndex].deductions
      );
      
      setEmployees(updatedEmployees);
      saveEmployees(updatedEmployees);

      // تسجيل المعاملة في الخزينة (للخصم والعلاوة فقط)
      const transaction: CashTransaction = {
        id: Date.now().toString(),
        type: transactionType,
        category: 'مرتب',
        amount: amount,
        description: description,
        relatedId: employee.id,
        relatedName: employee.name,
        date: new Date().toISOString().split('T')[0],
        paymentMethod: 'نقدي',
        notes: actionForm.notes
      };
      addCashTransaction(transaction);
    }

    toast({
      title: "تم تنفيذ الإجراء",
      description: description
    });

    closeActionDialog();
  };

  const generatePayrollReport = () => {
    const monthName = new Date(parseInt(selectedYear), parseInt(selectedMonth)).toLocaleDateString('ar-LY', { month: 'long', year: 'numeric' });
    
    const reportContent = `
كشف مرتبات - مصنع الأثاث
====================

شهر: ${monthName}
تاريخ الإعداد: ${new Date().toLocaleDateString('ar-LY')}

إجمالي المرتبات: ${formatLibyanDinar(totalSalaries)}
عدد الموظفين: ${employees.length}

تفصيل المرتبات:
${employees.map(emp => `
الاسم: ${emp.name}
المنصب: ${emp.position}
الراتب الأساسي: ${formatLibyanDinar(emp.baseSalary)}
العلاوات: ${formatLibyanDinar(emp.bonuses)}
الخصميات: ${formatLibyanDinar(emp.deductions)}
الصافي: ${formatLibyanDinar(emp.totalSalary)}
تاريخ التعيين: ${new Date(emp.hireDate).toLocaleDateString('ar-LY')}
الهاتف: ${emp.phone || 'غير محدد'}
`).join('\n')}

التوقيع: _______________
المدير المالي
`;

    const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `كشف_مرتبات_${monthName.replace(/\s/g, '_')}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const totalSalaries = employees.reduce((sum, emp) => sum + emp.totalSalary, 0);
  const totalBonuses = employees.reduce((sum, emp) => sum + emp.bonuses, 0);
  const totalDeductions = employees.reduce((sum, emp) => sum + emp.deductions, 0);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-800 flex items-center justify-center gap-3">
            <User className="h-8 w-8 text-blue-600" />
            إدارة مرتبات العاملين
          </h1>
          <p className="text-gray-600 mt-2">إدارة رواتب ومرتبات الموظفين</p>
          <div className="flex justify-center gap-2 mt-4">
            <Button onClick={generatePayrollReport} variant="outline">
              <Download className="h-4 w-4 mr-2" />
              تحميل كشف المرتبات
            </Button>
            {!isAdding && (
              <Button 
                onClick={() => setIsAdding(true)}
                className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                إضافة موظف جديد
              </Button>
            )}
          </div>
        </header>

        <div className="grid md:grid-cols-4 gap-6 mb-8">
          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-t-lg">
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-6 w-6" />
                إجمالي المرتبات
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <p className="text-3xl font-bold text-green-600">{formatLibyanDinar(totalSalaries)}</p>
              <p className="text-sm text-gray-600 mt-2">{employees.length} موظف</p>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardContent className="p-6">
              <p className="text-sm text-gray-600">إجمالي العلاوات</p>
              <p className="text-2xl font-bold text-blue-600">{formatLibyanDinar(totalBonuses)}</p>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardContent className="p-6">
              <p className="text-sm text-gray-600">إجمالي الخصميات</p>
              <p className="text-2xl font-bold text-red-600">{formatLibyanDinar(totalDeductions)}</p>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardContent className="p-6">
              <p className="text-sm text-gray-600">متوسط الراتب</p>
              <p className="text-2xl font-bold text-purple-600">
                {employees.length > 0 ? formatLibyanDinar(totalSalaries / employees.length) : formatLibyanDinar(0)}
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid md:grid-cols-2 gap-4 mb-6">
          <div className="space-y-2">
            <Label htmlFor="month">الشهر</Label>
            <Select value={selectedMonth} onValueChange={setSelectedMonth}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Array.from({length: 12}, (_, i) => (
                  <SelectItem key={i} value={i.toString()}>
                    {new Date(2024, i).toLocaleDateString('ar-LY', { month: 'long' })}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="year">السنة</Label>
            <Select value={selectedYear} onValueChange={setSelectedYear}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {[2023, 2024, 2025].map(year => (
                  <SelectItem key={year} value={year.toString()}>
                    {year}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {isAdding && (
          <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm mb-6">
            <CardHeader className="bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-t-lg">
              <CardTitle>{editingId ? "تعديل بيانات الموظف" : "إضافة موظف جديد"}</CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">اسم الموظف</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({...formData, name: e.target.value})}
                      placeholder="مثال: محمد أحمد"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="position">المنصب</Label>
                    <Input
                      id="position"
                      value={formData.position}
                      onChange={(e) => setFormData({...formData, position: e.target.value})}
                      placeholder="مثال: نجار، مساعد، مشرف"
                      required
                    />
                  </div>
                </div>
                <div className="grid md:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="baseSalary">الراتب الأساسي (د.ل)</Label>
                    <Input
                      id="baseSalary"
                      type="number"
                      value={formData.baseSalary}
                      onChange={(e) => setFormData({...formData, baseSalary: e.target.value})}
                      placeholder="0.00"
                      min="0"
                      step="0.01"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bonuses">العلاوات والمكافآت (د.ل)</Label>
                    <Input
                      id="bonuses"
                      type="number"
                      value={formData.bonuses}
                      onChange={(e) => setFormData({...formData, bonuses: e.target.value})}
                      placeholder="0.00"
                      min="0"
                      step="0.01"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="deductions">الخصميات (د.ل)</Label>
                    <Input
                      id="deductions"
                      type="number"
                      value={formData.deductions}
                      onChange={(e) => setFormData({...formData, deductions: e.target.value})}
                      placeholder="0.00"
                      min="0"
                      step="0.01"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">رقم الهاتف</Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => setFormData({...formData, phone: e.target.value})}
                      placeholder="************"
                    />
                  </div>
                </div>
                <div className="flex gap-2 justify-end">
                  <Button type="button" variant="outline" onClick={handleCancel}>إلغاء</Button>
                  <Button type="submit">{editingId ? "حفظ التعديلات" : "إضافة الموظف"}</Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-t-lg">
            <CardTitle>قائمة الموظفين ({employees.length})</CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            {employees.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full text-right">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="py-3 px-4 font-semibold text-gray-700">الاسم</th>
                      <th className="py-3 px-4 font-semibold text-gray-700">المنصب</th>
                      <th className="py-3 px-4 font-semibold text-gray-700">الراتب الأساسي</th>
                      <th className="py-3 px-4 font-semibold text-gray-700">العلاوات</th>
                      <th className="py-3 px-4 font-semibold text-gray-700">الخصميات</th>
                      <th className="py-3 px-4 font-semibold text-gray-700">المجموع</th>
                      <th className="py-3 px-4 font-semibold text-gray-700">تاريخ التعيين</th>
                      <th className="py-3 px-4 font-semibold text-gray-700">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {employees.map((employee) => (
                      <tr key={employee.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <div>
                            <p className="font-medium">{employee.name}</p>
                            {employee.phone && <p className="text-sm text-gray-600">{employee.phone}</p>}
                          </div>
                        </td>
                        <td className="py-3 px-4 text-gray-600">{employee.position}</td>
                        <td className="py-3 px-4">{formatLibyanDinar(employee.baseSalary)}</td>
                        <td className="py-3 px-4 text-green-600">{formatLibyanDinar(employee.bonuses)}</td>
                        <td className="py-3 px-4 text-red-600">{formatLibyanDinar(employee.deductions)}</td>
                        <td className="py-3 px-4 font-bold text-blue-600">{formatLibyanDinar(employee.totalSalary)}</td>
                        <td className="py-3 px-4">{new Date(employee.hireDate).toLocaleDateString('ar-LY')}</td>
                        <td className="py-3 px-4">
                          <div className="flex gap-1 flex-wrap">
                            <Button 
                              size="sm" 
                              variant="outline" 
                              onClick={() => openActionDialog('deduction', employee)}
                              className="h-8 px-2 text-red-600 hover:text-red-700"
                              title="تطبيق خصم"
                            >
                              <Minus className="h-3 w-3" />
                            </Button>
                            <Button 
                              size="sm" 
                              variant="outline" 
                              onClick={() => openActionDialog('bonus', employee)}
                              className="h-8 px-2 text-green-600 hover:text-green-700"
                              title="إضافة علاوة"
                            >
                              <TrendingUp className="h-3 w-3" />
                            </Button>
                            <Button 
                              size="sm" 
                              variant="outline" 
                              onClick={() => openActionDialog('payment', employee)}
                              className="h-8 px-2 text-blue-600 hover:text-blue-700"
                              title="دفع راتب"
                            >
                              <DollarSign className="h-3 w-3" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => handleEdit(employee)} className="h-8 px-2">
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => handleDelete(employee.id)} className="h-8 px-2 text-red-600 hover:text-red-700">
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-12">
                <User className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-600 mb-2">لا توجد موظفين مُضافين</h3>
                <p className="text-gray-500 mb-4">ابدأ بإضافة الموظفين ومرتباتهم</p>
                <Button 
                  onClick={() => setIsAdding(true)}
                  className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  إضافة أول موظف
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        <Dialog open={actionDialog.isOpen} onOpenChange={closeActionDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {actionDialog.type === 'deduction' && 'تطبيق خصم على الراتب'}
                {actionDialog.type === 'bonus' && 'إضافة علاوة للراتب'}
                {actionDialog.type === 'payment' && 'دفع راتب'}
              </DialogTitle>
            </DialogHeader>
            
            {actionDialog.employee && (
              <div className="space-y-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="font-medium">{actionDialog.employee.name}</p>
                  <p className="text-sm text-gray-600">{actionDialog.employee.position}</p>
                  <p className="text-sm text-gray-600">الراتب الحالي: {formatLibyanDinar(actionDialog.employee.totalSalary)}</p>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="amount">المبلغ (د.ل)</Label>
                  <Input
                    id="amount"
                    type="number"
                    value={actionForm.amount}
                    onChange={(e) => setActionForm({...actionForm, amount: e.target.value})}
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">السبب</Label>
                  <Input
                    id="description"
                    value={actionForm.description}
                    onChange={(e) => setActionForm({...actionForm, description: e.target.value})}
                    placeholder={
                      actionDialog.type === 'deduction' ? 'مثال: غياب، تأخير، خصم نظامي' :
                      actionDialog.type === 'bonus' ? 'مثال: مكافأة أداء، عمل إضافي' :
                      'دفع راتب شهر'
                    }
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="notes">ملاحظات إضافية</Label>
                  <Textarea
                    id="notes"
                    value={actionForm.notes}
                    onChange={(e) => setActionForm({...actionForm, notes: e.target.value})}
                    placeholder="ملاحظات اختيارية..."
                    rows={3}
                  />
                </div>
                
                <div className="flex gap-2 justify-end">
                  <Button type="button" variant="outline" onClick={closeActionDialog}>
                    إلغاء
                  </Button>
                  <Button 
                    onClick={handlePayrollAction}
                    disabled={!actionForm.amount}
                    className={
                      actionDialog.type === 'deduction' ? 'bg-red-600 hover:bg-red-700' :
                      actionDialog.type === 'bonus' ? 'bg-green-600 hover:bg-green-700' :
                      'bg-blue-600 hover:bg-blue-700'
                    }
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    تأكيد الإجراء
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default Salaries;
