// نظام إدارة البيانات المحلي للمصنع
export interface Material {
  id: string;
  name: string;
  description: string;
  pricePerSqm: number;
  category: string;
}

export interface Worker {
  id: string;
  name: string;
  specialty: string;
  pricePerSqm: number;
  phone?: string;
}

export interface Factory {
  id: string;
  name: string;
  specialty: string;
  pricePerSqm: number;
  location?: string;
}

export interface Designer {
  id: string;
  name: string;
  specialty: string;
  pricePerSqm: number;
  phone?: string;
}

export interface Invoice {
  id: string;
  projectId: string;
  customerName: string;
  customerPhone?: string;
  totalAmount: number;
  status: 'مبدئية' | 'تصنيع' | 'مكتملة' | 'ملغية';
  type: 'مبدئية' | 'تصنيع';
  createdAt: string;
  updatedAt?: string;
  notes?: string;
}

export interface Project {
  id: string;
  customerName: string;
  customerPhone?: string;
  area: number;
  furnitureType: string;
  selectedMaterial: Material;
  selectedWorker: Worker;
  selectedFactory: Factory;
  selectedDesigner: Designer;
  totalCost: number;
  breakdown: {
    materialCost: number;
    workerCost: number;
    factoryCost: number;
    designerCost: number;
  };
  paidAmount: number;
  remainingAmount: number;
  status: 'مكتمل' | 'قيد التنفيذ' | 'متأخر' | 'ملغي';
  invoiceStatus: 'مبدئية' | 'تصنيع' | 'مكتملة';
  createdAt: string;
  completedAt?: string;
  notes?: string;
}

export interface Customer {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  address?: string;
  totalProjects: number;
  totalSpent: number;
  createdAt: string;
}

export interface Employee {
  id: string;
  name: string;
  position: string;
  baseSalary: number;
  bonuses: number;
  deductions: number;
  totalSalary: number;
  phone?: string;
  hireDate: string;
}

export interface CashTransaction {
  id: string;
  type: 'دخل' | 'مصروف';
  category: 'مشروع' | 'مرتب' | 'مصروفات عامة' | 'شراء مواد' | 'صيانة' | 'كهرباء' | 'ايجار' | 'أخرى';
  amount: number;
  description: string;
  relatedId?: string;
  relatedName?: string;
  date: string;
  paymentMethod: 'نقدي' | 'تحويل بنكي' | 'شيك';
  notes?: string;
}

export interface CashSummary {
  totalIncome: number;
  totalExpenses: number;
  currentBalance: number;
  monthlyIncome: number;
  monthlyExpenses: number;
  projectPayments: number;
  salaryPayments: number;
  generalExpenses: number;
}

// وظائف إدارة البيانات
export const saveData = (key: string, data: any) => {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.error('Error saving data:', error);
  }
};

export const loadData = (key: string, defaultValue: any = []) => {
  try {
    const data = localStorage.getItem(key);
    return data ? JSON.parse(data) : defaultValue;
  } catch (error) {
    console.error('Error loading data:', error);
    return defaultValue;
  }
};

// وظائف إدارة المواد
export const getMaterials = (): Material[] => loadData('materials', []);
export const saveMaterials = (materials: Material[]) => saveData('materials', materials);

// وظائف إدارة العمال
export const getWorkers = (): Worker[] => loadData('workers', []);
export const saveWorkers = (workers: Worker[]) => saveData('workers', workers);

// وظائف إدارة المصانع
export const getFactories = (): Factory[] => loadData('factories', []);
export const saveFactories = (factories: Factory[]) => saveData('factories', factories);

// وظائف إدارة المصممين
export const getDesigners = (): Designer[] => loadData('designers', []);
export const saveDesigners = (designers: Designer[]) => saveData('designers', designers);

// وظائف إدارة المشاريع
export const getProjects = (): Project[] => loadData('projects', []);
export const saveProjects = (projects: Project[]) => saveData('projects', projects);

// وظائف إدارة الفواتير
export const getInvoices = (): Invoice[] => loadData('invoices', []);
export const saveInvoices = (invoices: Invoice[]) => saveData('invoices', invoices);

// وظائف إدارة العملاء
export const getCustomers = (): Customer[] => loadData('customers', []);
export const saveCustomers = (customers: Customer[]) => saveData('customers', customers);

export const addProject = (project: Project) => {
  const projects = getProjects();
  projects.push(project);
  saveProjects(projects);
  
  // إنشاء فاتورة مبدئية
  const invoice: Invoice = {
    id: Date.now().toString(),
    projectId: project.id,
    customerName: project.customerName,
    customerPhone: project.customerPhone,
    totalAmount: project.totalCost,
    status: 'مبدئية',
    type: 'مبدئية',
    createdAt: new Date().toISOString(),
    notes: `فاتورة مبدئية لمشروع ${project.furnitureType}`
  };
  
  const invoices = getInvoices();
  invoices.push(invoice);
  saveInvoices(invoices);
  
  // إضافة/تحديث بيانات العميل
  updateCustomerData(project.customerName, project.customerPhone, project.totalCost);
};

// تحديث بيانات العميل
export const updateCustomerData = (name: string, phone?: string, projectCost?: number) => {
  const customers = getCustomers();
  let customer = customers.find(c => c.name === name);
  
  if (customer) {
    customer.totalProjects += 1;
    if (projectCost) customer.totalSpent += projectCost;
    if (phone && !customer.phone) customer.phone = phone;
  } else {
    customer = {
      id: Date.now().toString(),
      name,
      phone,
      totalProjects: 1,
      totalSpent: projectCost || 0,
      createdAt: new Date().toISOString()
    };
    customers.push(customer);
  }
  
  saveCustomers(customers);
};

// تحويل الفاتورة من مبدئية إلى تصنيع
export const convertInvoiceToManufacturing = (invoiceId: string) => {
  const invoices = getInvoices();
  const invoice = invoices.find(inv => inv.id === invoiceId);
  
  if (invoice && invoice.status === 'مبدئية') {
    invoice.status = 'تصنيع';
    invoice.type = 'تصنيع';
    invoice.updatedAt = new Date().toISOString();
    saveInvoices(invoices);
    
    // إضافة معاملة دخل للخزينة عند تحويل الفاتورة لتصنيع
    const transaction: CashTransaction = {
      id: Date.now().toString(),
      type: 'دخل',
      category: 'مشروع',
      amount: invoice.totalAmount,
      description: `إيراد من فاتورة تصنيع - ${invoice.customerName}`,
      relatedId: invoice.projectId,
      relatedName: invoice.customerName,
      date: new Date().toISOString().split('T')[0],
      paymentMethod: 'نقدي',
      notes: `تحويل فاتورة رقم ${invoice.id} إلى تصنيع`
    };
    addCashTransaction(transaction);
  }
};

// الحصول على فواتير عميل معين
export const getCustomerInvoices = (customerName: string): Invoice[] => {
  const invoices = getInvoices();
  return invoices.filter(invoice => invoice.customerName === customerName);
};

// الحصول على مشاريع عميل معين
export const getCustomerProjects = (customerName: string): Project[] => {
  const projects = getProjects();
  return projects.filter(project => project.customerName === customerName);
};

// وظائف إدارة الموظفين
export const getEmployees = (): Employee[] => loadData('employees', []);
export const saveEmployees = (employees: Employee[]) => saveData('employees', employees);

// وظائف إدارة الخزينة
export const getCashTransactions = (): CashTransaction[] => loadData('cashTransactions', []);
export const saveCashTransactions = (transactions: CashTransaction[]) => saveData('cashTransactions', transactions);

export const addCashTransaction = (transaction: CashTransaction) => {
  const transactions = getCashTransactions();
  transactions.push(transaction);
  saveCashTransactions(transactions);
};

export const getCashSummary = (): CashSummary => {
  const transactions = getCashTransactions();
  const currentMonth = new Date().toISOString().slice(0, 7);
  
  const totalIncome = transactions
    .filter(t => t.type === 'دخل')
    .reduce((sum, t) => sum + t.amount, 0);
    
  const totalExpenses = transactions
    .filter(t => t.type === 'مصروف')
    .reduce((sum, t) => sum + t.amount, 0);
    
  const monthlyIncome = transactions
    .filter(t => t.type === 'دخل' && t.date.startsWith(currentMonth))
    .reduce((sum, t) => sum + t.amount, 0);
    
  const monthlyExpenses = transactions
    .filter(t => t.type === 'مصروف' && t.date.startsWith(currentMonth))
    .reduce((sum, t) => sum + t.amount, 0);
    
  const projectPayments = transactions
    .filter(t => t.type === 'دخل' && t.category === 'مشروع')
    .reduce((sum, t) => sum + t.amount, 0);
    
  const salaryPayments = transactions
    .filter(t => t.type === 'مصروف' && t.category === 'مرتب')
    .reduce((sum, t) => sum + t.amount, 0);
    
  const generalExpenses = transactions
    .filter(t => t.type === 'مصروف' && t.category !== 'مرتب')
    .reduce((sum, t) => sum + t.amount, 0);

  return {
    totalIncome,
    totalExpenses,
    currentBalance: totalIncome - totalExpenses,
    monthlyIncome,
    monthlyExpenses,
    projectPayments,
    salaryPayments,
    generalExpenses
  };
};

// وظيفة دفع راتب موظف
export const paySalary = (employeeId: string, amount: number, notes?: string) => {
  const employees = getEmployees();
  const employee = employees.find(emp => emp.id === employeeId);
  
  if (employee) {
    const transaction: CashTransaction = {
      id: Date.now().toString(),
      type: 'مصروف',
      category: 'مرتب',
      amount: amount,
      description: `راتب ${employee.name} - ${employee.position}`,
      relatedId: employee.id,
      relatedName: employee.name,
      date: new Date().toISOString().split('T')[0],
      paymentMethod: 'نقدي',
      notes: notes
    };
    addCashTransaction(transaction);
  }
};

// وظيفة تسديد باقي دفعة مشروع
export const payProjectBalance = (projectId: string, amount: number, notes?: string) => {
  const projects = getProjects();
  const project = projects.find(p => p.id === projectId);
  
  if (project) {
    const transaction: CashTransaction = {
      id: Date.now().toString(),
      type: 'دخل',
      category: 'مشروع',
      amount: amount,
      description: `دفعة متبقية من مشروع ${project.customerName}`,
      relatedId: project.id,
      relatedName: project.customerName,
      date: new Date().toISOString().split('T')[0],
      paymentMethod: 'نقدي',
      notes: notes
    };
    addCashTransaction(transaction);
    
    project.paidAmount += amount;
    project.remainingAmount -= amount;
    if (project.remainingAmount <= 0) {
      project.status = 'مكتمل';
      project.completedAt = new Date().toISOString();
    }
    saveProjects(projects);
  }
};

// إضافة بيانات افتراضية إذا لم تكن موجودة
export const initializeDefaultData = () => {
  const materials = getMaterials();
  if (materials.length === 0) {
    const defaultMaterials: Material[] = [
      {
        id: "1",
        name: "خشب البلوط العادي",
        description: "خشب بلوط طبيعي للأثاث العادي",
        pricePerSqm: 450,
        category: "خشب"
      },
      {
        id: "2",
        name: "خشب الجوز الفاخر",
        description: "خشب جوز عالي الجودة للأثاث الفاخر",
        pricePerSqm: 750,
        category: "خشب"
      },
      {
        id: "3",
        name: "MDF عالي الجودة",
        description: "ألواح MDF مقاومة للرطوبة",
        pricePerSqm: 280,
        category: "ألواح"
      }
    ];
    saveMaterials(defaultMaterials);
  }

  const workers = getWorkers();
  if (workers.length === 0) {
    const defaultWorkers: Worker[] = [
      {
        id: "1",
        name: "محمد أحمد",
        specialty: "نجار رئيسي",
        pricePerSqm: 150,
        phone: "************"
      },
      {
        id: "2", 
        name: "علي حسن",
        specialty: "مساعد نجار",
        pricePerSqm: 100,
        phone: "************"
      }
    ];
    saveWorkers(defaultWorkers);
  }

  const factories = getFactories();
  if (factories.length === 0) {
    const defaultFactories: Factory[] = [
      {
        id: "1",
        name: "مصنع الأثاث الحديث",
        specialty: "أثاث مكتبي وغرف نوم",
        pricePerSqm: 200,
        location: "طرابلس"
      },
      {
        id: "2",
        name: "مصنع النجمة",
        specialty: "أثاث مطابخ وحمامات",
        pricePerSqm: 250,
        location: "بنغازي"
      }
    ];
    saveFactories(defaultFactories);
  }

  const designers = getDesigners();
  if (designers.length === 0) {
    const defaultDesigners: Designer[] = [
      {
        id: "1",
        name: "فاطمة محمد",
        specialty: "تصميم داخلي حديث",
        pricePerSqm: 80,
        phone: "************"
      },
      {
        id: "2",
        name: "خالد علي",
        specialty: "تصميم كلاسيكي",
        pricePerSqm: 100,
        phone: "************"
      }
    ];
    saveDesigners(defaultDesigners);
  }
};
