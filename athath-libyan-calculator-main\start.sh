#!/bin/bash

echo ""
echo "========================================"
echo "    نظام إدارة مصنع الأثاث"
echo "    تطبيق سطح المكتب"
echo "========================================"
echo ""

echo "🔍 فحص Node.js..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً"
    echo "📥 تحميل من: https://nodejs.org"
    exit 1
fi

echo "✅ Node.js مثبت ($(node --version))"
echo ""

echo "📦 فحص التبعيات..."
if [ ! -d "node_modules" ]; then
    echo "🔄 تثبيت التبعيات..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ فشل في تثبيت التبعيات"
        exit 1
    fi
else
    echo "✅ التبعيات مثبتة"
fi

echo ""
echo "🚀 تشغيل التطبيق..."
echo ""
npm run electron:dev

if [ $? -ne 0 ]; then
    echo ""
    echo "❌ فشل في تشغيل التطبيق"
    echo "🔧 جرب الأوامر التالية:"
    echo "   npm run clean"
    echo "   npm install"
    echo "   npm run electron:dev"
fi
