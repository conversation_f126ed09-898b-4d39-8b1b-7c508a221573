
import { Clock, MapPin } from "lucide-react";

const DateFormatter = () => {
  const now = new Date();
  
  // Format date as DD/MM/YYYY
  const formatDate = (date: Date): string => {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  // Format time as HH:MM
  const formatTime = (date: Date): string => {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  };

  const currentDate = formatDate(now);
  const currentTime = formatTime(now);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <span className="text-gray-700 flex items-center gap-2">
          <Clock className="h-4 w-4" />
          التاريخ:
        </span>
        <span className="font-mono text-lg font-semibold">{currentDate}</span>
      </div>
      
      <div className="flex items-center justify-between">
        <span className="text-gray-700 flex items-center gap-2">
          <Clock className="h-4 w-4" />
          الوقت:
        </span>
        <span className="font-mono text-lg font-semibold">{currentTime}</span>
      </div>

      <div className="flex items-center justify-between">
        <span className="text-gray-700 flex items-center gap-2">
          <MapPin className="h-4 w-4" />
          المنطقة الزمنية:
        </span>
        <span className="font-semibold">ليبيا (UTC+2)</span>
      </div>

      <div className="mt-4 p-3 bg-purple-100 rounded-lg border border-purple-200">
        <p className="text-sm text-purple-800 text-center">
          تم إنشاء هذا الحساب بتاريخ <strong>{currentDate}</strong> في الساعة <strong>{currentTime}</strong>
        </p>
      </div>
    </div>
  );
};

export default DateFormatter;
