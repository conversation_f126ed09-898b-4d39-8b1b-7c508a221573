# 🏭 نظام إدارة مصنع الأثاث المتكامل

## 📋 وصف المشروع

نظام شامل لإدارة جميع عمليات مصنع الأثاث من حساب التكاليف إلى إدارة الخزينة والموظفين.

**متوفر كـ:**
- 🌐 تطبيق ويب
- 💻 تطبيق سطح مكتب (Electron)

**URL**: https://lovable.dev/projects/adc88773-3f44-40b5-b2fc-4ac0f7c95946

## 🚀 تشغيل التطبيق

### تطبيق سطح المكتب (مُوصى به)

**تشغيل سريع:**
```bash
# Windows
start.bat

# Mac/Linux
./start.sh
```

**تشغيل يدوي:**
```bash
# تثبيت التبعيات
npm install

# تشغيل التطبيق
npm run electron:dev

# إنشاء ملف التثبيت
npm run build
```

### تطبيق الويب

```bash
# تثبيت التبعيات
npm install

# تشغيل خادم التطوير
npm run dev

# بناء للإنتاج
npm run build
```

## 🔧 فحص الإعداد

```bash
# فحص أن جميع الملفات موجودة
npm run test:setup
```

## 🛠️ التقنيات المستخدمة

### Frontend
- **React 18** مع **TypeScript**
- **Vite** كأداة البناء
- **Tailwind CSS** للتصميم
- **shadcn/ui** لمكونات واجهة المستخدم
- **React Router** للتنقل
- **React Query** لإدارة البيانات
- **Lucide React** للأيقونات

### Desktop App
- **Electron** لتطبيق سطح المكتب
- **Electron Builder** للبناء والتوزيع
- **Concurrently** لتشغيل عدة عمليات
- **Wait-on** لانتظار الخادم

## 🎯 الميزات الرئيسية

- 🧮 **حاسبة التكلفة**: حساب تكلفة المشاريع بدقة
- 📦 **إدارة المواد**: إدارة المواد والعمال والمصانع
- 💰 **إدارة الخزينة**: تتبع المدفوعات والمصروفات
- 📊 **التقارير**: تقارير شاملة للمشاريع والأرباح
- 👥 **إدارة المرتبات**: إدارة الموظفين ومرتباتهم
- 🧾 **إدارة الفواتير**: فواتير مبدئية وتصنيع
- 👤 **إدارة العملاء**: قاعدة بيانات العملاء
- 🔒 **تخزين آمن**: حفظ البيانات محلياً

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/adc88773-3f44-40b5-b2fc-4ac0f7c95946) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
