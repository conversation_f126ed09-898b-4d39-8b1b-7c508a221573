
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { formatLibyanDinar } from "@/utils/calculations";
import { Calculator, Receipt, Download } from "lucide-react";

interface PriceDisplayProps {
  totalCost: number;
  area: number;
  breakdownCosts: {
    materialCost: number;
    workerCost: number;
    factoryCost: number;
    designerCost: number;
    customerName: string;
    selectedMaterial: string;
    selectedWorker: string;
    selectedFactory: string;
    selectedDesigner: string;
  } | null;
}

const PriceDisplay = ({ totalCost, area, breakdownCosts }: PriceDisplayProps) => {
  const costPerSqm = totalCost / area;
  const paidAmount = totalCost * 0.7; // 70% مدفوع
  const remainingAmount = totalCost * 0.3; // 30% باقي

  const handleGenerateInvoice = () => {
    const invoiceContent = `
فاتورة مصنع الأثاث
================

اسم العميل: ${breakdownCosts?.customerName || 'غير محدد'}
التاريخ: ${new Date().toLocaleDateString('ar-LY')}
المساحة: ${area} متر مربع

تفاصيل الطلب:
نوع الأثاث: ${breakdownCosts?.selectedMaterial}
العامل: ${breakdownCosts?.selectedWorker}
المصنع: ${breakdownCosts?.selectedFactory}
المصمم: ${breakdownCosts?.selectedDesigner}

تفصيل التكاليف:
- تكلفة المواد: ${formatLibyanDinar(breakdownCosts?.materialCost || 0)}
- تكلفة العمالة: ${formatLibyanDinar(breakdownCosts?.workerCost || 0)}
- تكلفة المصنع: ${formatLibyanDinar(breakdownCosts?.factoryCost || 0)}
- تكلفة التصميم: ${formatLibyanDinar(breakdownCosts?.designerCost || 0)}

التكلفة الإجمالية: ${formatLibyanDinar(totalCost)}

تقسيم الدفع:
- المبلغ المدفوع (70%): ${formatLibyanDinar(paidAmount)}
- المبلغ المتبقي (30%): ${formatLibyanDinar(remainingAmount)}
`;

    const blob = new Blob([invoiceContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `فاتورة_${breakdownCosts?.customerName || 'عميل'}_${new Date().toLocaleDateString('ar-LY').replace(/\//g, '-')}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader className="bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-t-lg">
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-6 w-6" />
            نتيجة الحساب
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6 space-y-4">
          <div className="text-center space-y-2">
            <p className="text-lg text-gray-600">التكلفة الإجمالية</p>
            <p className="text-4xl font-bold text-green-600">{formatLibyanDinar(totalCost)}</p>
            <p className="text-sm text-gray-500">
              لمساحة {area} متر مربع • {formatLibyanDinar(costPerSqm)} لكل متر مربع
            </p>
          </div>

          {breakdownCosts && (
            <div className="border-t pt-4 space-y-3">
              <h3 className="font-semibold text-lg text-gray-800">تفصيل التكاليف:</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>تكلفة المواد:</span>
                  <span className="font-semibold">{formatLibyanDinar(breakdownCosts.materialCost)}</span>
                </div>
                <div className="flex justify-between">
                  <span>تكلفة العمالة:</span>
                  <span className="font-semibold">{formatLibyanDinar(breakdownCosts.workerCost)}</span>
                </div>
                <div className="flex justify-between">
                  <span>تكلفة المصنع:</span>
                  <span className="font-semibold">{formatLibyanDinar(breakdownCosts.factoryCost)}</span>
                </div>
                <div className="flex justify-between">
                  <span>تكلفة التصميم:</span>
                  <span className="font-semibold">{formatLibyanDinar(breakdownCosts.designerCost)}</span>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader className="bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-t-lg">
          <CardTitle className="flex items-center gap-2">
            <Receipt className="h-6 w-6" />
            الفاتورة وتقسيم الدفع
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6 space-y-4">
          <div className="space-y-3">
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <h4 className="font-semibold text-green-800 mb-2">المبلغ المدفوع (70%)</h4>
              <p className="text-2xl font-bold text-green-700">{formatLibyanDinar(paidAmount)}</p>
            </div>
            
            <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
              <h4 className="font-semibold text-orange-800 mb-2">المبلغ المتبقي (30%)</h4>
              <p className="text-2xl font-bold text-orange-700">{formatLibyanDinar(remainingAmount)}</p>
            </div>
          </div>

          <Button 
            onClick={handleGenerateInvoice}
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            تحميل الفاتورة
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default PriceDisplay;
