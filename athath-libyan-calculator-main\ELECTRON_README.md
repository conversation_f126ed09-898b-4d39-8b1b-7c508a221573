# نظام إدارة مصنع الأثاث - تطبيق سطح المكتب

## تشغيل التطبيق في وضع التطوير

### المتطلبات
- Node.js (الإصدار 16 أو أحدث)
- npm أو yarn

### خطوات التشغيل

1. **تثبيت التبعيات:**
```bash
npm install
```

2. **تشغيل التطبيق في وضع التطوير:**
```bash
npm run electron:dev
```

3. **بناء التطبيق للإنتاج:**
```bash
npm run build
```

4. **تشغيل Electron مع الملفات المبنية:**
```bash
npm run electron
```

5. **إنشاء ملف التثبيت:**
```bash
npm run electron:dist
```

## الأوامر المتاحة

- `npm run dev` - تشغيل خادم التطوير للويب
- `npm run build` - بناء التطبيق للإنتاج
- `npm run electron` - تشغيل Electron مع الملفات المبنية
- `npm run electron:dev` - تشغيل التطبيق في وضع التطوير
- `npm run electron:pack` - إنشاء حزمة التطبيق
- `npm run electron:dist` - إنشاء ملف التثبيت

## ملفات التوزيع

بعد تشغيل `npm run electron:dist`، ستجد ملفات التثبيت في مجلد `dist-electron`:

- **Windows**: ملف `.exe` للتثبيت
- **macOS**: ملف `.dmg`
- **Linux**: ملف `.AppImage`

## الميزات المضافة لتطبيق سطح المكتب

- قائمة تطبيق عربية كاملة
- اختصارات لوحة المفاتيح
- حفظ وفتح الملفات
- طباعة التقارير
- إشعارات النظام
- نافذة مخصصة بحجم مناسب
- أيقونة التطبيق

## الأمان

- تم تعطيل Node.js integration
- تم تفعيل Context Isolation
- تم استخدام preload script للأمان
- تم منع فتح نوافذ خارجية

## التخصيص

يمكنك تخصيص التطبيق من خلال:

1. **تغيير الأيقونة**: استبدل `public/icon.png` بأيقونة مخصصة
2. **تعديل القائمة**: عدل ملف `public/electron.js`
3. **إضافة ميزات جديدة**: استخدم IPC للتواصل بين العمليات

## استكشاف الأخطاء

### مشكلة في تشغيل التطبيق:
```bash
# تأكد من تثبيت جميع التبعيات
npm install

# امسح cache
npm run clean
npm install
```

### مشكلة في البناء:
```bash
# تأكد من بناء الويب أولاً
npm run build

# ثم بناء Electron
npm run electron:pack
```

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.
