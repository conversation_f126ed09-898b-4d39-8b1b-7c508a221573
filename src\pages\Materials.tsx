
import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { ArrowLeft, Plus, Edit, Trash2, Package, Hammer, Factory, Pen } from "lucide-react";
import { Link } from "react-router-dom";
import { formatLibyanDinar } from "@/utils/calculations";
import { 
  getMaterials, 
  saveMaterials,
  getWorkers,
  saveWorkers,
  getFactories,
  saveFactories,
  getDesigners,
  saveDesigners,
  Material,
  Worker,
  Factory as FactoryType,
  Designer
} from "@/utils/dataManager";
import { useToast } from "@/hooks/use-toast";
import Navbar from "@/components/Navbar";

const Materials = () => {
  const { toast } = useToast();
  
  // البيانات
  const [materials, setMaterials] = useState<Material[]>([]);
  const [workers, setWorkers] = useState<Worker[]>([]);
  const [factories, setFactories] = useState<FactoryType[]>([]);
  const [designers, setDesigners] = useState<Designer[]>([]);

  // حالات النماذج
  const [isAddingMaterial, setIsAddingMaterial] = useState(false);
  const [isAddingWorker, setIsAddingWorker] = useState(false);
  const [isAddingFactory, setIsAddingFactory] = useState(false);
  const [isAddingDesigner, setIsAddingDesigner] = useState(false);

  // معرفات التعديل
  const [editingMaterialId, setEditingMaterialId] = useState<string | null>(null);
  const [editingWorkerId, setEditingWorkerId] = useState<string | null>(null);
  const [editingFactoryId, setEditingFactoryId] = useState<string | null>(null);
  const [editingDesignerId, setEditingDesignerId] = useState<string | null>(null);

  // بيانات النماذج
  const [materialForm, setMaterialForm] = useState({
    name: "", description: "", pricePerSqm: "", category: ""
  });
  
  const [workerForm, setWorkerForm] = useState({
    name: "", specialty: "", pricePerSqm: "", phone: ""
  });
  
  const [factoryForm, setFactoryForm] = useState({
    name: "", specialty: "", pricePerSqm: "", location: ""
  });
  
  const [designerForm, setDesignerForm] = useState({
    name: "", specialty: "", pricePerSqm: "", phone: ""
  });

  useEffect(() => {
    loadAllData();
  }, []);

  const loadAllData = () => {
    setMaterials(getMaterials());
    setWorkers(getWorkers());
    setFactories(getFactories());
    setDesigners(getDesigners());
  };

  // وظائف إدارة المواد
  const handleSubmitMaterial = (e: React.FormEvent) => {
    e.preventDefault();
    if (!materialForm.name || !materialForm.pricePerSqm) return;

    const newMaterial: Material = {
      id: editingMaterialId || Date.now().toString(),
      name: materialForm.name,
      description: materialForm.description,
      pricePerSqm: parseFloat(materialForm.pricePerSqm),
      category: materialForm.category
    };

    const updatedMaterials = editingMaterialId 
      ? materials.map(m => m.id === editingMaterialId ? newMaterial : m)
      : [...materials, newMaterial];

    setMaterials(updatedMaterials);
    saveMaterials(updatedMaterials);
    resetMaterialForm();
    
    toast({
      title: editingMaterialId ? "تم تحديث المادة" : "تم إضافة المادة",
      description: "تم حفظ البيانات بنجاح"
    });
  };

  const resetMaterialForm = () => {
    setMaterialForm({ name: "", description: "", pricePerSqm: "", category: "" });
    setIsAddingMaterial(false);
    setEditingMaterialId(null);
  };

  // وظائف إدارة العمال
  const handleSubmitWorker = (e: React.FormEvent) => {
    e.preventDefault();
    if (!workerForm.name || !workerForm.pricePerSqm) return;

    const newWorker: Worker = {
      id: editingWorkerId || Date.now().toString(),
      name: workerForm.name,
      specialty: workerForm.specialty,
      pricePerSqm: parseFloat(workerForm.pricePerSqm),
      phone: workerForm.phone
    };

    const updatedWorkers = editingWorkerId 
      ? workers.map(w => w.id === editingWorkerId ? newWorker : w)
      : [...workers, newWorker];

    setWorkers(updatedWorkers);
    saveWorkers(updatedWorkers);
    resetWorkerForm();
    
    toast({
      title: editingWorkerId ? "تم تحديث العامل" : "تم إضافة العامل",
      description: "تم حفظ البيانات بنجاح"
    });
  };

  const resetWorkerForm = () => {
    setWorkerForm({ name: "", specialty: "", pricePerSqm: "", phone: "" });
    setIsAddingWorker(false);
    setEditingWorkerId(null);
  };

  // وظائف إدارة المصانع
  const handleSubmitFactory = (e: React.FormEvent) => {
    e.preventDefault();
    if (!factoryForm.name || !factoryForm.pricePerSqm) return;

    const newFactory: FactoryType = {
      id: editingFactoryId || Date.now().toString(),
      name: factoryForm.name,
      specialty: factoryForm.specialty,
      pricePerSqm: parseFloat(factoryForm.pricePerSqm),
      location: factoryForm.location
    };

    const updatedFactories = editingFactoryId 
      ? factories.map(f => f.id === editingFactoryId ? newFactory : f)
      : [...factories, newFactory];

    setFactories(updatedFactories);
    saveFactories(updatedFactories);
    resetFactoryForm();
    
    toast({
      title: editingFactoryId ? "تم تحديث المصنع" : "تم إضافة المصنع",
      description: "تم حفظ البيانات بنجاح"
    });
  };

  const resetFactoryForm = () => {
    setFactoryForm({ name: "", specialty: "", pricePerSqm: "", location: "" });
    setIsAddingFactory(false);
    setEditingFactoryId(null);
  };

  // وظائف إدارة المصممين
  const handleSubmitDesigner = (e: React.FormEvent) => {
    e.preventDefault();
    if (!designerForm.name || !designerForm.pricePerSqm) return;

    const newDesigner: Designer = {
      id: editingDesignerId || Date.now().toString(),
      name: designerForm.name,
      specialty: designerForm.specialty,
      pricePerSqm: parseFloat(designerForm.pricePerSqm),
      phone: designerForm.phone
    };

    const updatedDesigners = editingDesignerId 
      ? designers.map(d => d.id === editingDesignerId ? newDesigner : d)
      : [...designers, newDesigner];

    setDesigners(updatedDesigners);
    saveDesigners(updatedDesigners);
    resetDesignerForm();
    
    toast({
      title: editingDesignerId ? "تم تحديث المصمم" : "تم إضافة المصمم",
      description: "تم حفظ البيانات بنجاح"
    });
  };

  const resetDesignerForm = () => {
    setDesignerForm({ name: "", specialty: "", pricePerSqm: "", phone: "" });
    setIsAddingDesigner(false);
    setEditingDesignerId(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        <header className="flex items-center gap-4 mb-8">
          <Link to="/">
            <Button variant="outline" size="sm" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              العودة للحاسبة
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl md:text-4xl font-bold text-gray-800 flex items-center gap-3">
              <Package className="h-8 w-8 text-blue-600" />
              إدارة موارد المصنع
            </h1>
            <p className="text-gray-600 mt-2">إدارة شاملة للمواد والعمال والمصانع والمصممين</p>
          </div>
        </header>

        <Tabs defaultValue="materials" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="materials" className="flex items-center gap-2">
              <Package className="h-4 w-4" />
              المواد
            </TabsTrigger>
            <TabsTrigger value="workers" className="flex items-center gap-2">
              <Hammer className="h-4 w-4" />
              العمال
            </TabsTrigger>
            <TabsTrigger value="factories" className="flex items-center gap-2">
              <Factory className="h-4 w-4" />
              المصانع
            </TabsTrigger>
            <TabsTrigger value="designers" className="flex items-center gap-2">
              <Pen className="h-4 w-4" />
              المصممين
            </TabsTrigger>
          </TabsList>

          {/* تبويبة المواد */}
          <TabsContent value="materials" className="space-y-6">
            {!isAddingMaterial && (
              <div className="flex justify-end">
                <Button onClick={() => setIsAddingMaterial(true)} className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  إضافة مادة جديدة
                </Button>
              </div>
            )}

            {isAddingMaterial && (
              <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                <CardHeader className="bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-t-lg">
                  <CardTitle>{editingMaterialId ? "تعديل المادة" : "إضافة مادة جديدة"}</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <form onSubmit={handleSubmitMaterial} className="space-y-4">
                    
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="materialName">اسم المادة</Label>
                        <Input
                          id="materialName"
                          value={materialForm.name}
                          onChange={(e) => setMaterialForm({...materialForm, name: e.target.value})}
                          placeholder="مثال: خشب البلوط"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="materialCategory">الفئة</Label>
                        <Input
                          id="materialCategory"
                          value={materialForm.category}
                          onChange={(e) => setMaterialForm({...materialForm, category: e.target.value})}
                          placeholder="مثال: خشب، معدن، قماش"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="materialDescription">الوصف</Label>
                      <Textarea
                        id="materialDescription"
                        value={materialForm.description}
                        onChange={(e) => setMaterialForm({...materialForm, description: e.target.value})}
                        placeholder="وصف المادة ومواصفاتها..."
                        rows={3}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="materialPrice">السعر لكل متر مربع (د.ل)</Label>
                      <Input
                        id="materialPrice"
                        type="number"
                        value={materialForm.pricePerSqm}
                        onChange={(e) => setMaterialForm({...materialForm, pricePerSqm: e.target.value})}
                        placeholder="0.00"
                        min="0"
                        step="0.01"
                        required
                      />
                    </div>
                    <div className="flex gap-2 justify-end">
                      <Button type="button" variant="outline" onClick={resetMaterialForm}>إلغاء</Button>
                      <Button type="submit">{editingMaterialId ? "حفظ التعديلات" : "إضافة المادة"}</Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            )}

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {materials.map((material) => (
                <Card key={material.id} className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg text-gray-800">{material.name}</CardTitle>
                        {material.category && <span className="text-sm text-blue-600 font-medium">{material.category}</span>}
                      </div>
                      <div className="flex gap-1">
                        <Button size="sm" variant="outline" onClick={() => {
                          setMaterialForm({
                            name: material.name,
                            description: material.description,
                            pricePerSqm: material.pricePerSqm.toString(),
                            category: material.category
                          });
                          setEditingMaterialId(material.id);
                          setIsAddingMaterial(true);
                        }}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => {
                          const updated = materials.filter(m => m.id !== material.id);
                          setMaterials(updated);
                          saveMaterials(updated);
                          toast({ title: "تم حذف المادة", description: "تم حذف المادة بنجاح" });
                        }}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    {material.description && <p className="text-gray-600 text-sm mb-3">{material.description}</p>}
                    <div className="text-2xl font-bold text-green-600">{formatLibyanDinar(material.pricePerSqm)}/م²</div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* باقي التبويبات بنفس النمط... */}
          <TabsContent value="workers" className="space-y-6">
            {/* نماذج وعرض العمال */}
            
            {/* Workers Tab Content */}
            {!isAddingWorker && (
              <div className="flex justify-end">
                <Button onClick={() => setIsAddingWorker(true)} className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  إضافة عامل جديد
                </Button>
              </div>
            )}

            {isAddingWorker && (
              <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                <CardHeader className="bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-t-lg">
                  <CardTitle>{editingWorkerId ? "تعديل العامل" : "إضافة عامل جديد"}</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <form onSubmit={handleSubmitWorker} className="space-y-4">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="workerName">اسم العامل</Label>
                        <Input
                          id="workerName"
                          value={workerForm.name}
                          onChange={(e) => setWorkerForm({...workerForm, name: e.target.value})}
                          placeholder="مثال: محمد أحمد"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="workerSpecialty">التخصص</Label>
                        <Input
                          id="workerSpecialty"
                          value={workerForm.specialty}
                          onChange={(e) => setWorkerForm({...workerForm, specialty: e.target.value})}
                          placeholder="مثال: نجار، دهان"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="workerPhone">رقم الهاتف</Label>
                      <Input
                        id="workerPhone"
                        type="tel"
                        value={workerForm.phone}
                        onChange={(e) => setWorkerForm({...workerForm, phone: e.target.value})}
                        placeholder="09X-XXXXXXX"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="workerPrice">الأجر لكل متر مربع (د.ل)</Label>
                      <Input
                        id="workerPrice"
                        type="number"
                        value={workerForm.pricePerSqm}
                        onChange={(e) => setWorkerForm({...workerForm, pricePerSqm: e.target.value})}
                        placeholder="0.00"
                        min="0"
                        step="0.01"
                        required
                      />
                    </div>
                    <div className="flex gap-2 justify-end">
                      <Button type="button" variant="outline" onClick={resetWorkerForm}>إلغاء</Button>
                      <Button type="submit">{editingWorkerId ? "حفظ التعديلات" : "إضافة عامل"}</Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            )}

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {workers.map((worker) => (
                <Card key={worker.id} className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg text-gray-800">{worker.name}</CardTitle>
                        <span className="text-sm text-blue-600 font-medium">{worker.specialty}</span>
                      </div>
                      <div className="flex gap-1">
                        <Button size="sm" variant="outline" onClick={() => {
                          setWorkerForm({
                            name: worker.name,
                            specialty: worker.specialty,
                            pricePerSqm: worker.pricePerSqm.toString(),
                            phone: worker.phone || ""
                          });
                          setEditingWorkerId(worker.id);
                          setIsAddingWorker(true);
                        }}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => {
                          const updated = workers.filter(w => w.id !== worker.id);
                          setWorkers(updated);
                          saveWorkers(updated);
                          toast({ title: "تم حذف العامل", description: "تم حذف العامل بنجاح" });
                        }}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    {worker.phone && <p className="text-gray-600 text-sm mb-3">هاتف: {worker.phone}</p>}
                    <div className="text-2xl font-bold text-green-600">{formatLibyanDinar(worker.pricePerSqm)}/م²</div>
                  </CardContent>
                </Card>
              ))}
            </div>
          
          </TabsContent>

          <TabsContent value="factories" className="space-y-6">
            {/* نماذج وعرض المصانع */}
            
            {/* Factories Tab Content */}
            {!isAddingFactory && (
              <div className="flex justify-end">
                <Button onClick={() => setIsAddingFactory(true)} className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  إضافة مصنع جديد
                </Button>
              </div>
            )}

            {isAddingFactory && (
              <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                <CardHeader className="bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-t-lg">
                  <CardTitle>{editingFactoryId ? "تعديل المصنع" : "إضافة مصنع جديد"}</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <form onSubmit={handleSubmitFactory} className="space-y-4">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="factoryName">اسم المصنع</Label>
                        <Input
                          id="factoryName"
                          value={factoryForm.name}
                          onChange={(e) => setFactoryForm({...factoryForm, name: e.target.value})}
                          placeholder="مثال: مصنع الأثاث الحديث"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="factorySpecialty">التخصص</Label>
                        <Input
                          id="factorySpecialty"
                          value={factoryForm.specialty}
                          onChange={(e) => setFactoryForm({...factoryForm, specialty: e.target.value})}
                          placeholder="مثال: أثاث مكتبي، غرف نوم"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="factoryLocation">الموقع</Label>
                      <Input
                        id="factoryLocation"
                        value={factoryForm.location}
                        onChange={(e) => setFactoryForm({...factoryForm, location: e.target.value})}
                        placeholder="مثال: طرابلس"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="factoryPrice">التكلفة لكل متر مربع (د.ل)</Label>
                      <Input
                        id="factoryPrice"
                        type="number"
                        value={factoryForm.pricePerSqm}
                        onChange={(e) => setFactoryForm({...factoryForm, pricePerSqm: e.target.value})}
                        placeholder="0.00"
                        min="0"
                        step="0.01"
                        required
                      />
                    </div>
                    <div className="flex gap-2 justify-end">
                      <Button type="button" variant="outline" onClick={resetFactoryForm}>إلغاء</Button>
                      <Button type="submit">{editingFactoryId ? "حفظ التعديلات" : "إضافة مصنع"}</Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            )}

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {factories.map((factory) => (
                <Card key={factory.id} className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg text-gray-800">{factory.name}</CardTitle>
                        <span className="text-sm text-blue-600 font-medium">{factory.specialty}</span>
                      </div>
                      <div className="flex gap-1">
                        <Button size="sm" variant="outline" onClick={() => {
                          setFactoryForm({
                            name: factory.name,
                            specialty: factory.specialty,
                            pricePerSqm: factory.pricePerSqm.toString(),
                            location: factory.location || ""
                          });
                          setEditingFactoryId(factory.id);
                          setIsAddingFactory(true);
                        }}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => {
                          const updated = factories.filter(f => f.id !== factory.id);
                          setFactories(updated);
                          saveFactories(updated);
                          toast({ title: "تم حذف المصنع", description: "تم حذف المصنع بنجاح" });
                        }}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    {factory.location && <p className="text-gray-600 text-sm mb-3">الموقع: {factory.location}</p>}
                    <div className="text-2xl font-bold text-green-600">{formatLibyanDinar(factory.pricePerSqm)}/م²</div>
                  </CardContent>
                </Card>
              ))}
            </div>
          
          </TabsContent>

          <TabsContent value="designers" className="space-y-6">
            {/* نماذج وعرض المصممين */}
            
            {/* Designers Tab Content */}
            {!isAddingDesigner && (
              <div className="flex justify-end">
                <Button onClick={() => setIsAddingDesigner(true)} className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  إضافة مصمم جديد
                </Button>
              </div>
            )}

            {isAddingDesigner && (
              <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                <CardHeader className="bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-t-lg">
                  <CardTitle>{editingDesignerId ? "تعديل المصمم" : "إضافة مصمم جديد"}</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <form onSubmit={handleSubmitDesigner} className="space-y-4">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="designerName">اسم المصمم</Label>
                        <Input
                          id="designerName"
                          value={designerForm.name}
                          onChange={(e) => setDesignerForm({...designerForm, name: e.target.value})}
                          placeholder="مثال: فاطمة محمد"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="designerSpecialty">التخصص</Label>
                        <Input
                          id="designerSpecialty"
                          value={designerForm.specialty}
                          onChange={(e) => setDesignerForm({...designerForm, specialty: e.target.value})}
                          placeholder="مثال: تصميم داخلي، تصميم خارجي"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="designerPhone">رقم الهاتف</Label>
                      <Input
                        id="designerPhone"
                        type="tel"
                        value={designerForm.phone}
                        onChange={(e) => setDesignerForm({...designerForm, phone: e.target.value})}
                        placeholder="09X-XXXXXXX"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="designerPrice">الأتعاب لكل متر مربع (د.ل)</Label>
                      <Input
                        id="designerPrice"
                        type="number"
                        value={designerForm.pricePerSqm}
                        onChange={(e) => setDesignerForm({...designerForm, pricePerSqm: e.target.value})}
                        placeholder="0.00"
                        min="0"
                        step="0.01"
                        required
                      />
                    </div>
                    <div className="flex gap-2 justify-end">
                      <Button type="button" variant="outline" onClick={resetDesignerForm}>إلغاء</Button>
                      <Button type="submit">{editingDesignerId ? "حفظ التعديلات" : "إضافة مصمم"}</Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            )}

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {designers.map((designer) => (
                <Card key={designer.id} className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg text-gray-800">{designer.name}</CardTitle>
                        <span className="text-sm text-blue-600 font-medium">{designer.specialty}</span>
                      </div>
                      <div className="flex gap-1">
                        <Button size="sm" variant="outline" onClick={() => {
                          setDesignerForm({
                            name: designer.name,
                            specialty: designer.specialty,
                            pricePerSqm: designer.pricePerSqm.toString(),
                            phone: designer.phone || ""
                          });
                          setEditingDesignerId(designer.id);
                          setIsAddingDesigner(true);
                        }}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => {
                          const updated = designers.filter(d => d.id !== designer.id);
                          setDesigners(updated);
                          saveDesigners(updated);
                          toast({ title: "تم حذف المصمم", description: "تم حذف المصمم بنجاح" });
                        }}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    {designer.phone && <p className="text-gray-600 text-sm mb-3">هاتف: {designer.phone}</p>}
                    <div className="text-2xl font-bold text-green-600">{formatLibyanDinar(designer.pricePerSqm)}/م²</div>
                  </CardContent>
                </Card>
              ))}
            </div>
          
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Materials;
