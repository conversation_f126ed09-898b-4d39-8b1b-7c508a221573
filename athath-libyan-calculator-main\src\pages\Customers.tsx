
import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Users, FileText, Phone, Calendar, Eye, ArrowLeft } from "lucide-react";
import { Link } from "react-router-dom";
import Navbar from "@/components/Navbar";
import { formatLibyanDinar } from "@/utils/calculations";
import { 
  getCustomers, 
  getCustomerInvoices, 
  getCustomerProjects, 
  getInvoices,
  convertInvoiceToManufacturing,
  Customer, 
  Invoice,
  Project 
} from "@/utils/dataManager";
import { useToast } from "@/hooks/use-toast";

const Customers = () => {
  const { toast } = useToast();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [customerInvoices, setCustomerInvoices] = useState<Invoice[]>([]);
  const [customerProjects, setCustomerProjects] = useState<Project[]>([]);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    loadCustomers();
  }, []);

  const loadCustomers = () => {
    setCustomers(getCustomers());
  };

  const loadCustomerData = (customer: Customer) => {
    setSelectedCustomer(customer);
    setCustomerInvoices(getCustomerInvoices(customer.name));
    setCustomerProjects(getCustomerProjects(customer.name));
  };

  const handleConvertToManufacturing = (invoiceId: string) => {
    convertInvoiceToManufacturing(invoiceId);
    if (selectedCustomer) {
      loadCustomerData(selectedCustomer);
    }
    toast({
      title: "تم تحويل الفاتورة",
      description: "تم تحويل الفاتورة إلى فاتورة تصنيع وتسجيل الإيراد",
    });
  };

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (customer.phone && customer.phone.includes(searchTerm))
  );

  const getInvoiceStatusColor = (status: string) => {
    switch (status) {
      case 'مبدئية':
        return 'text-orange-600 bg-orange-50';
      case 'تصنيع':
        return 'text-blue-600 bg-blue-50';
      case 'مكتملة':
        return 'text-green-600 bg-green-50';
      case 'ملغية':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-800 flex items-center justify-center gap-3">
            <Users className="h-8 w-8 text-blue-600" />
            إدارة العملاء
          </h1>
          <p className="text-gray-600 mt-2">عرض بيانات العملاء وفواتيرهم</p>
        </header>

        <div className="grid lg:grid-cols-2 gap-6">
          {/* قائمة العملاء */}
          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-t-lg">
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                قائمة العملاء ({filteredCustomers.length})
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="mb-4">
                <Input
                  placeholder="البحث بالاسم أو رقم الهاتف..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              <div className="space-y-3 max-h-96 overflow-y-auto">
                {filteredCustomers.map((customer) => (
                  <Card 
                    key={customer.id} 
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      selectedCustomer?.id === customer.id ? 'ring-2 ring-blue-500' : ''
                    }`}
                    onClick={() => loadCustomerData(customer)}
                  >
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-semibold text-gray-800">{customer.name}</h3>
                          {customer.phone && (
                            <p className="text-sm text-gray-600 flex items-center gap-1 mt-1">
                              <Phone className="h-3 w-3" />
                              {customer.phone}
                            </p>
                          )}
                          <p className="text-sm text-gray-500 flex items-center gap-1 mt-1">
                            <Calendar className="h-3 w-3" />
                            {new Date(customer.createdAt).toLocaleDateString('ar-LY')}
                          </p>
                        </div>
                        <div className="text-left">
                          <p className="text-sm text-gray-600">المشاريع: {customer.totalProjects}</p>
                          <p className="text-sm font-semibold text-green-600">
                            {formatLibyanDinar(customer.totalSpent)}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {filteredCustomers.length === 0 && (
                <div className="text-center py-8">
                  <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">لا يوجد عملاء</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* تفاصيل العميل */}
          <div className="space-y-6">
            {selectedCustomer ? (
              <>
                {/* معلومات العميل */}
                <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader className="bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-t-lg">
                    <CardTitle className="flex items-center gap-2">
                      <Eye className="h-5 w-5" />
                      تفاصيل العميل: {selectedCustomer.name}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-600">الاسم</label>
                        <p className="font-semibold">{selectedCustomer.name}</p>
                      </div>
                      {selectedCustomer.phone && (
                        <div>
                          <label className="text-sm font-medium text-gray-600">الهاتف</label>
                          <p className="font-semibold">{selectedCustomer.phone}</p>
                        </div>
                      )}
                      <div>
                        <label className="text-sm font-medium text-gray-600">عدد المشاريع</label>
                        <p className="font-semibold">{selectedCustomer.totalProjects}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">إجمالي المبلغ</label>
                        <p className="font-semibold text-green-600">
                          {formatLibyanDinar(selectedCustomer.totalSpent)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* فواتير العميل */}
                <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader className="bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-t-lg">
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      فواتير العميل ({customerInvoices.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    {customerInvoices.length > 0 ? (
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead className="text-right">رقم الفاتورة</TableHead>
                              <TableHead className="text-right">النوع</TableHead>
                              <TableHead className="text-right">المبلغ</TableHead>
                              <TableHead className="text-right">الحالة</TableHead>
                              <TableHead className="text-right">التاريخ</TableHead>
                              <TableHead className="text-right">الإجراءات</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {customerInvoices.map((invoice) => (
                              <TableRow key={invoice.id}>
                                <TableCell className="font-medium">#{invoice.id}</TableCell>
                                <TableCell>{invoice.type}</TableCell>
                                <TableCell>{formatLibyanDinar(invoice.totalAmount)}</TableCell>
                                <TableCell>
                                  <span className={`px-2 py-1 rounded text-xs font-medium ${getInvoiceStatusColor(invoice.status)}`}>
                                    {invoice.status}
                                  </span>
                                </TableCell>
                                <TableCell>
                                  {new Date(invoice.createdAt).toLocaleDateString('ar-LY')}
                                </TableCell>
                                <TableCell>
                                  {invoice.status === 'مبدئية' && (
                                    <Button
                                      size="sm"
                                      onClick={() => handleConvertToManufacturing(invoice.id)}
                                      className="bg-blue-600 hover:bg-blue-700"
                                    >
                                      تحويل للتصنيع
                                    </Button>
                                  )}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-600">لا توجد فواتير لهذا العميل</p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* مشاريع العميل */}
                <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader className="bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-t-lg">
                    <CardTitle>مشاريع العميل ({customerProjects.length})</CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    {customerProjects.length > 0 ? (
                      <div className="space-y-3">
                        {customerProjects.map((project) => (
                          <Card key={project.id} className="border border-gray-200">
                            <CardContent className="p-4">
                              <div className="flex justify-between items-start">
                                <div>
                                  <h4 className="font-semibold">{project.furnitureType}</h4>
                                  <p className="text-sm text-gray-600">المساحة: {project.area} م²</p>
                                  <p className="text-sm text-gray-600">
                                    {new Date(project.createdAt).toLocaleDateString('ar-LY')}
                                  </p>
                                </div>
                                <div className="text-left">
                                  <p className="font-semibold">{formatLibyanDinar(project.totalCost)}</p>
                                  <p className="text-sm text-gray-600">الحالة: {project.status}</p>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-600">لا توجد مشاريع لهذا العميل</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </>
            ) : (
              <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                <CardContent className="p-12 text-center">
                  <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-600 mb-2">اختر عميل</h3>
                  <p className="text-gray-500">اختر عميل من القائمة لعرض تفاصيله وفواتيره</p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Customers;
